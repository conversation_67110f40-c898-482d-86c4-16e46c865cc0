Download the React DevTools for a better development experience: https://reactjs.org/link/react-devtools
2useObras.ts:22 Uncaught TypeError: queryKeys.obras.list is not a function
    at useObras (useObras.ts:22:31)
    at Dashboard (Dashboard.tsx:37:46)
    at renderWithHooks (chunk-NFC5BX5N.js?v=87503299:11548:26)
    at mountIndeterminateComponent (chunk-NFC5BX5N.js?v=87503299:14926:21)
    at beginWork (chunk-NFC5BX5N.js?v=87503299:15914:22)
    at HTMLUnknownElement.callCallback2 (chunk-NFC5BX5N.js?v=87503299:3674:22)
    at Object.invokeGuardedCallbackDev (chunk-NFC5BX5N.js?v=87503299:3699:24)
    at invokeGuardedCallback (chunk-NFC5BX5N.js?v=87503299:3733:39)
    at beginWork$1 (chunk-NFC5BX5N.js?v=87503299:19765:15)
    at performUnitOfWork (chunk-NFC5BX5N.js?v=87503299:19198:20)Understand this error
chunk-NFC5BX5N.js?v=87503299:14032 The above error occurred in the <Dashboard> component:

    at Dashboard (http://localhost:8080/src/pages/Dashboard.tsx:37:48)
    at ProtectedRoute (http://localhost:8080/src/contexts/auth/ProtectedRoutes.tsx:26:34)
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=87503299:4088:5)
    at Outlet (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=87503299:4494:26)
    at AppContent
    at RenderedRoute (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=87503299:4088:5)
    at Routes (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=87503299:4558:5)
    at _a (http://localhost:8080/node_modules/.vite/deps/react-helmet-async.js?v=87503299:624:5)
    at AuthProvider (http://localhost:8080/src/contexts/auth/AuthContext.tsx:32:32)
    at LoadingProvider (http://localhost:8080/src/contexts/LoadingContext.tsx:31:35)
    at ThemeProvider (http://localhost:8080/src/providers/theme-provider.tsx:26:33)
    at QueryClientProvider (http://localhost:8080/node_modules/.vite/deps/@tanstack_react-query.js?v=87503299:2933:3)
    at App
    at Router (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=87503299:4501:15)
    at BrowserRouter (http://localhost:8080/node_modules/.vite/deps/react-router-dom.js?v=87503299:5247:5)

Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://reactjs.org/link/error-boundaries to learn more about error boundaries.
logCapturedError @ chunk-NFC5BX5N.js?v=87503299:14032Understand this error
chunk-NFC5BX5N.js?v=87503299:19413 Uncaught TypeError: queryKeys.obras.list is not a function
    at useObras (useObras.ts:22:31)
    at Dashboard (Dashboard.tsx:37:46)
    at renderWithHooks (chunk-NFC5BX5N.js?v=87503299:11548:26)
    at mountIndeterminateComponent (chunk-NFC5BX5N.js?v=87503299:14926:21)
    at beginWork (chunk-NFC5BX5N.js?v=87503299:15914:22)
    at beginWork$1 (chunk-NFC5BX5N.js?v=87503299:19753:22)
    at performUnitOfWork (chunk-NFC5BX5N.js?v=87503299:19198:20)
    at workLoopSync (chunk-NFC5BX5N.js?v=87503299:19137:13)
    at renderRootSync (chunk-NFC5BX5N.js?v=87503299:19116:15)
    at recoverFromConcurrentError (chunk-NFC5BX5N.js?v=87503299:18736:28)Understand this error