import { supabase } from "@/integrations/supabase/client";

export interface TrialInfo {
  isTrialing: boolean;
  isExpired: boolean;
  daysRemaining: number;
  hoursRemaining: number;
  endDate: Date | null;
  startDate: Date | null;
}

export const getTrialInfo = (subscription: any): TrialInfo => {
  if (!subscription || subscription.status !== "trialing") {
    return {
      isTrialing: false,
      isExpired: false,
      daysRemaining: 0,
      hoursRemaining: 0,
      endDate: null,
      startDate: null,
    };
  }

  const now = new Date();
  const endDate = new Date(subscription.current_period_end);
  const startDate = new Date(subscription.current_period_start);

  const remainingMs = endDate.getTime() - now.getTime();
  const daysRemaining = Math.max(
    0,
    Math.ceil(remainingMs / (1000 * 60 * 60 * 24))
  );
  const hoursRemaining = Math.max(0, Math.ceil(remainingMs / (1000 * 60 * 60)));

  return {
    isTrialing: true,
    isExpired: remainingMs <= 0,
    daysRemaining,
    hoursRemaining,
    endDate,
    startDate,
  };
};

export const formatTimeRemaining = (trialInfo: TrialInfo): string => {
  if (!trialInfo.isTrialing) return "";

  if (trialInfo.isExpired) return "Expirado";

  if (trialInfo.daysRemaining > 1) {
    return `${trialInfo.daysRemaining} dias restantes`;
  }

  if (trialInfo.daysRemaining === 1) {
    return "Último dia do trial";
  }

  return `${trialInfo.hoursRemaining}h restantes`;
};

export const shouldShowUrgentAlert = (trialInfo: TrialInfo): boolean => {
  return trialInfo.isTrialing && trialInfo.daysRemaining <= 2;
};

export const createTrialSubscription = async (userId: string) => {
  try {
    const { data, error } = await supabase.functions.invoke(
      "create-trial-subscription",
      {
        body: { userId },
      }
    );

    if (error) throw error;

    return { success: true, data };
  } catch (error) {
    console.error("Erro ao criar trial:", error);
    return { success: false, error };
  }
};

export const getTrialQuotaMessage = (
  feature: "chat" | "budget" | "contract" | "sinapi",
  current: number,
  limit: number,
  canUse: boolean
): string => {
  switch (feature) {
    case "chat":
      if (!canUse) {
        return "Limite de 3 requests diários atingido. Aguarde até amanhã ou faça upgrade.";
      }
      return `${limit - current} requests restantes hoje`;

    case "budget":
      if (!canUse) {
        return "Você já usou seu 1 orçamento do trial. Faça upgrade para orçamentos ilimitados.";
      }
      return "1 orçamento disponível no trial";

    case "contract":
      return "Análise de contratos disponível apenas nos planos pagos.";

    case "sinapi":
      if (!canUse) {
        return "Limite diário de consultas SINAPI atingido.";
      }
      return `${limit - current} consultas restantes hoje`;

    default:
      return "";
  }
};

export const getUpgradeMessage = (trialInfo: TrialInfo): string => {
  if (!trialInfo.isTrialing) {
    return "Faça upgrade para acessar todas as funcionalidades premium.";
  }

  if (trialInfo.isExpired) {
    return "Seu trial expirou. Faça upgrade para continuar usando o ObraVision.";
  }

  if (trialInfo.daysRemaining <= 1) {
    return "Seu trial expira em breve! Aproveite nossa oferta especial.";
  }

  if (trialInfo.daysRemaining <= 3) {
    return "Apenas alguns dias restantes. Garanta seu acesso ilimitado!";
  }

  return "Aproveite o trial e conheça todas as funcionalidades.";
};

export const getTrialProgress = (trialInfo: TrialInfo): number => {
  if (!trialInfo.isTrialing || !trialInfo.startDate || !trialInfo.endDate) {
    return 0;
  }

  const totalDays = Math.ceil(
    (trialInfo.endDate.getTime() - trialInfo.startDate.getTime()) /
      (1000 * 60 * 60 * 24)
  );

  const elapsedDays = totalDays - trialInfo.daysRemaining;
  
  return Math.min(100, Math.max(0, (elapsedDays / totalDays) * 100));
};